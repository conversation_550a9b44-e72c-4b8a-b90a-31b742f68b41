export enum BoxStatus {
    Open = 0,
    Closed = 1,
    Transferred = 2,
}

// Type definition for status configuration
export interface BoxStatusConfig {
    value: number
    label: string
    description: string
    className: string
    icon: string
    color: string
}

// Comprehensive status configuration for both dropdowns and tables
export const getBoxStatusConfig = (
    t: (key: string) => string,
): Record<BoxStatus, BoxStatusConfig> => ({
    [BoxStatus.Open]: {
        value: 0,
        label: t('nav.boxes.open'),
        description: 'Box is currently open and available for file additions',
        className: 'bg-green-100 text-green-800 border-green-200',
        icon: 'TbBoxOpen',
        color: 'green',
    },
    [BoxStatus.Closed]: {
        value: 1,
        label: t('nav.boxes.closed'),
        description: 'Box has been closed and no more files can be added',
        className: 'bg-red-100 text-red-800 border-red-200',
        icon: 'TbBox',
        color: 'red',
    },
    [BoxStatus.Transferred]: {
        value: 2,
        label: t('nav.boxes.transferred'),
        description: 'Box has been transferred to warehouse storage',
        className: 'bg-blue-100 text-blue-800 border-blue-200',
        icon: 'TbTruck',
        color: 'blue',
    },
})

// Helper function to get status options for dropdowns
export const getBoxStatusOptions = (
    t: (key: string) => string,
    options: {
        includeAll?: boolean
        allowedStatuses?: BoxStatus[]
        excludeStatuses?: BoxStatus[]
    } = {}
) => {
    const {
        includeAll = true,
        allowedStatuses,
        excludeStatuses = []
    } = options

    const statusConfig = getBoxStatusConfig(t)
    let statusOptions = Object.values(statusConfig).map((config) => ({
        value: config.value.toString(),
        label: config.label,
    }))

    // If allowedStatuses is provided, only include those statuses
    if (allowedStatuses && allowedStatuses.length > 0) {
        statusOptions = statusOptions.filter((option) =>
            allowedStatuses.includes(parseInt(option.value) as BoxStatus)
        )
    }

    // Exclude specific statuses if provided
    if (excludeStatuses.length > 0) {
        statusOptions = statusOptions.filter((option) =>
            !excludeStatuses.includes(parseInt(option.value) as BoxStatus)
        )
    }

    // Add "All" option if requested
    if (includeAll) {
        return [{ value: '', label: t('nav.shared.allStatuses') }, ...statusOptions]
    }

    return statusOptions
}

// Helper function to get status configuration by value
export const getBoxStatusByValue = (
    status: number,
    t: (key: string) => string,
): BoxStatusConfig | null => {
    const statusConfig = getBoxStatusConfig(t)
    return statusConfig[status as BoxStatus] || null
}

// Legacy function for backward compatibility (deprecated)
export const getBoxStatusStyle = (status: BoxStatus) => {
    switch (status) {
        case BoxStatus.Open:
            return {
                color: '#4CAF50', // Green
                backgroundColor: '#E8F5E9',
                borderRadius: '4px',
                padding: '4px 8px',
                fontWeight: 500,
            }
        case BoxStatus.Closed:
            return {
                color: '#F44336', // Red
                backgroundColor: '#FFEBEE',
                borderRadius: '4px',
                padding: '4px 8px',
                fontWeight: 500,
            }
        case BoxStatus.Transferred:
            return {
                color: '#2196F3', // Blue
                backgroundColor: '#E3F2FD',
                borderRadius: '4px',
                padding: '4px 8px',
                fontWeight: 500,
            }
        default:
            return {}
    }
}
