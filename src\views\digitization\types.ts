// Enum for document statuses
export enum DocumentStatus {
    open = 0,
    close = 1,
}

// Type definition for status configuration
export interface DocumentStatusConfig {
    value: number
    label: string
    className: string
    icon: string
    color: string
}

export const getDocumentStatusConfig = (
    status: DocumentStatus,
    t: (key: string) => string,
): Record<DocumentStatus, DocumentStatusConfig> => {
    return {
        [DocumentStatus.open]: {
            value: DocumentStatus.open,
            label: t('digitization.documentStatus.open'),
            className: 'bg-green-100 text-green-800 border-green-200',
            icon: 'TbBoxOpen',
            color: 'green',
        },
        [DocumentStatus.close]: {
            value: DocumentStatus.close,
            label: t('digitization.documentStatus.close'),
            className: 'bg-red-100 text-red-800 border-red-200',
            icon: 'TbBox',
            color: 'red',
        },
    }
}

export const getDocumentStatusOptions = (
    t: (key: string) => string,
) => {
