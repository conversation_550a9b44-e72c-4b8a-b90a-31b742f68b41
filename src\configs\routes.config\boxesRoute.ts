import { lazy } from 'react'
import { BOXES_PREFIX_PATH } from '@/constants/route.constant'
import type { Routes } from '@/@types/routes'
import { USER, ADMIN } from '@/constants/roles.constant'

const boxesRoute: Routes = [
    {
        key: 'boxes.list',
        path: `${BOXES_PREFIX_PATH}`,
        component: lazy(() => import('@/views/boxes/Boxes')),
        authority: [USER],
        meta: {
            pageContainerType: 'default',
        },
    },
    {
        key: 'linkFiles.list',
        path: `${BOXES_PREFIX_PATH}/linkFiles`,
        component: lazy(() => import('@/views/boxes/LinkFiles')),
        authority: [USER],
        meta: {
            pageContainerType: 'default',
        },
    },
    {
        key: 'transferBoxes.list',
        path: `${BOXES_PREFIX_PATH}/transferBoxes`,
        component: lazy(() => import('@/views/boxes/TransferBoxes')),
        authority: [ADMIN],
        meta: {
            pageContainerType: 'default',
        },
    },
    {
        key: 'transferredBoxes.list',
        path: `${BOXES_PREFIX_PATH}/transferred`,
        component: lazy(() => import('@/views/boxes/Transfered')),
        authority: [ADMIN],
        meta: {
            pageContainerType: 'default',
        },
    },
    {
        key: 'boxes.allBoxes',
        path: `${BOXES_PREFIX_PATH}/all`,
        component: lazy(() => import('@/views/boxes/AllBoxes')),
        authority: [ADMIN],
        meta: {
            pageContainerType: 'default',
        },
    },
    // {
    //     key: 'boxes.details',
    //     path: `${BOXES_PREFIX_PATH}/:boxId`,
    //     component: lazy(() => import('@/views/boxes/BoxDetails')),
    //     authority: [ADMIN, USER],
    //     meta: {
    //         pageContainerType: 'default',
    //     },
    // },
]

export default boxesRoute
