export type Warehouse = {
    id: string
    name: string
    organizationalNodeName: string
    responsiblePersonName: string
    villageNameAr: string
    cityOrDistrictNameAr: string
    cityOrDistrictNameEn: string
    governorateNameAr: string
    governorateNameEn: string
    status: number
    numberOfAreas: number
}

export type WarehouseMobile = {
    mobileNumber: string
    note: string
}

export type WarehouseForm = {
    name: string
    organizationalNodeCode: string
    responsiblePersonName: string
    districtOrVillageCode: string
    detailedAddress: string
    maintenancePhoneNumber: string
    securityPhoneNumber: string
    notes: string
    warehouseMobiles: WarehouseMobile[]
}

export type WarehouseDetails = {
    id: string
    name: string
    organizationalNodeCode: string
    organizationalNodeName: string
    responsiblePersonName: string
    districtOrVillageCode: string
    villageNameAr: string
    cityOrDistrictNameAr: string
    cityOrDistrictNameEn: string
    governorateNameAr: string
    governorateNameEn: string
    countryNameAr: string
    countryNameEn: string
    detailedAddress: string
    maintenancePhoneNumber: string
    securityPhoneNumber: string
    notes: string
    status: number
    warehouseMobiles: {
        id: number
        mobileNumber: string
        note: string
    }[]
    areas: area[]
    createdAt: string
    createdByID: string
    updatedAt: string
    updatedByID: string
}

export type area = {
    id: string
    width: number
    height: number
    numberOfUnits: number
    numberOfShelves: number
    numberOfBoxes: number
    availableBoxes: number
    units?: Unit[]
}

export type Unit = {
    id: string
    width: number
    height: number
    numberOfShelves: number
    numberOfBoxes: number
    availableBoxes: number
    shelves?: Shelf[]
}

export type Shelf = {
    id: string
    width: number
    height: number
    numberOfBoxes: number
    availableBoxes: number
}

export type WarehouseStructure = {
    numberOfAreas: number
    areaLength: number
    areaWidth: number
    numberOfUnitsPerArea: number
    unitLength: number
    unitWidth: number
    numberOfShelvesPerUnit: number
    shelfLength: number
    shelfWidth: number
    numberOfBoxesPerShelf: number
}

export type WarehouseUnit = {
    numberOfUnits: number
    unitLength: number
    unitWidth: number
    numberOfShelvesPerUnit: number
    shelfLength: number
    shelfWidth: number
    numberOfBoxesPerShelf: number
}

export type WarehouseShelf = {
    numberOfShelves: number
    width: number
    height: number
    numberOfBoxes: number
}

export type WarehouseShelfCapacity = {
    id: string
    unitId: string
    maxBoxes: number
    currentBoxes: number
    availableBoxes: number
    utilizationPercentage: number
    isFullCapacity: boolean
    isNearCapacity: boolean
    length: number
    width: number
}

// Transfer Request Types
export type TransferOperation = {
    transferId: string
    boxesCount: number
    warehouseId: string
    warehouseName: string
    createByFullName: string
    createdAt: string
}

export type BoxLocation = {
    warehouseId: string
    areaId: string
    unitId: string
    shelfId: string
    placedAt: string
    reason: string
    locationDescription: string
}

export type TransferBox = {
    boxId: string
    filesCount: number
    pagesCount: number
    currentLocation: BoxLocation
}

export type TransferOperationDetails = {
    transferOperationId: string
    warehouse: {
        warehouseId: string
        name: string
    }
    totalBoxesCount: number
    totalFilesCount: number
    totalPagesCount: number
    notes: string
    createdAt: string
    createdBy: string
    boxes: TransferBox[]
}

export type TransfersResponse = {
    items: TransferOperation[]
    totalCount: number
    pageCount: number
}
