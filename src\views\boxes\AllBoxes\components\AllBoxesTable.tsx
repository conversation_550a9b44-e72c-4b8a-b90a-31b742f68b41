import { useMemo } from 'react'
import { useSearchParams } from 'react-router-dom'
import DataTable from '@/components/shared/DataTable'
import type { ColumnDef } from '@/components/shared/DataTable'
import useTranslation from '@/utils/hooks/useTranslation'
import { Box } from '@/@types/box'
import { useGetBoxes } from '@/hooks/boxes'
import BoxStatus from '../../components/BoxStatus'

const AllBoxesTable = () => {
    const { t } = useTranslation()
    const [searchParams, setSearchParams] = useSearchParams()

    // Read all filter parameters from URL
    const pageNumber = parseInt(searchParams.get('pageNumber') || '1', 10)
    const pageSize = parseInt(searchParams.get('pageSize') || '10', 10)
    const organizationalNodeId =
        searchParams.get('organizationalNodeId') || undefined
    const status = searchParams.get('status')
        ? parseInt(searchParams.get('status')!, 10)
        : undefined

    const { Boxes, isLoading, pagination } = useGetBoxes({
        organizationalNodeId,
        pageNumber,
        pageSize,
        boxStatus: status,
    })

    const handlePaginationChange = (page: number) => {
        const newSearchParams = new URLSearchParams(searchParams)
        newSearchParams.set('pageNumber', page.toString())
        setSearchParams(newSearchParams)
    }

    const handlePageSizeChange = (pageSize: number) => {
        const newSearchParams = new URLSearchParams(searchParams)
        newSearchParams.set('pageSize', pageSize.toString())
        newSearchParams.set('pageNumber', '1')
        setSearchParams(newSearchParams)
    }

    const tableColumns: ColumnDef<Box>[] = useMemo(() => {
        const cols: ColumnDef<Box>[] = [
            {
                header: t('nav.boxes.boxId'),
                accessorKey: 'boxId',
                cell: ({ row }) => (
                    <div className="font-mono text-sm">
                        {row.original.boxId}
                    </div>
                ),
            },
            {
                header: t('nav.boxes.organization'),
                accessorKey: 'organizationalNodeName',
                cell: ({ row }) => (
                    <div className="font-medium">
                        {row.original.organizationalNodeName}
                    </div>
                ),
            },
            {
                header: t('nav.boxes.filesCount'),
                accessorKey: 'numberOfFiles',
                cell: ({ row }) => (
                    <div className="text-center">
                        <span className="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-blue-100 text-blue-800 border border-blue-200">
                            {row.original.numberOfFiles}
                        </span>
                    </div>
                ),
            },
            {
                header: t('nav.boxes.closureStrap'),
                accessorKey: 'closureStrapSerialNumber',
                cell: ({ row }) => (
                    <div className="font-mono text-sm">
                        {row.original.closureStrapSerialNumber || 'N/A'}
                    </div>
                ),
            },
            {
                header: t('nav.boxes.status'),
                accessorKey: 'boxStatus',
                cell: ({ row }) => (
                    <BoxStatus
                        status={row.original.boxStatus}
                        variant="badge"
                    />
                ),
                enableSorting: false,
            },
            // {
            //     header: t('nav.shared.actions'),
            //     accessorKey: 'action',
            //     cell: ({ row }) => <Actions boxData={row.original} />,
            //     enableSorting: false,
            // },
        ]
        return cols
    }, [t])

    return (
        <>
            <DataTable
                selectable
                columns={tableColumns}
                data={Boxes || []}
                loading={isLoading}
                skeletonAvatarColumns={[0]}
                skeletonAvatarProps={{ width: 14, height: 14 }}
                cellBorder={true}
                pagingData={{
                    total: pagination?.totalCount || 0,
                    pageIndex: pagination?.pageNumber || 1,
                    pageSize: pagination?.pageSize || 10,
                }}
                onPaginationChange={handlePaginationChange}
                onSelectChange={handlePageSizeChange}
            />
        </>
    )
}

export default AllBoxesTable
