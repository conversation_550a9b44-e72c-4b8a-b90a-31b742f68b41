import { useState } from 'react'
import { useSearchParams } from 'react-router-dom'
import Input from '@/components/ui/Input'
import Select from '@/components/ui/Select'
import useTranslation from '@/utils/hooks/useTranslation'
import { TbSearch, TbFilter } from 'react-icons/tb'
import { useGetOrgsStructure } from '@/hooks/orgs'
import BoxStatusSelect from '../../components/BoxStatusSelect'

const AllBoxesTableTools = () => {
    const { t } = useTranslation()
    const [searchParams, setSearchParams] = useSearchParams()
    const [searchTerm, setSearchTerm] = useState('')

    // Get organizations for dropdown
    const { flattenedNodes, isLoading: isLoadingOrgs } = useGetOrgsStructure()

    // Read current filter values from URL
    const selectedOrganizationId =
        searchParams.get('organizationalNodeId') || ''
    const selectedStatus = searchParams.get('status') || ''

    // Transform organizations into options
    const organizationOptions = [
        { value: '', label: t('nav.shared.allOrganizations') },
        ...(flattenedNodes?.map((org) => ({
            value: org.code,
            label: `${org.name} (${org.code})`,
        })) || []),
    ]

    const handleStatusChange = (status: string) => {
        const newSearchParams = new URLSearchParams(searchParams)
        if (status) {
            newSearchParams.set('status', status)
        } else {
            newSearchParams.delete('status')
        }
        newSearchParams.set('pageNumber', '1')
        setSearchParams(newSearchParams)
    }

    const handleOrganizationChange = (
        selectedOption: { value: string; label: string } | null,
    ) => {
        const organizationId = selectedOption?.value || ''
        const newSearchParams = new URLSearchParams(searchParams)
        if (organizationId) {
            newSearchParams.set('organizationalNodeId', organizationId)
        } else {
            newSearchParams.delete('organizationalNodeId')
        }
        newSearchParams.set('pageNumber', '1')
        setSearchParams(newSearchParams)
    }

    const handleSearchChange = (e: React.ChangeEvent<HTMLInputElement>) => {
        setSearchTerm(e.target.value)
        // TODO: Implement search functionality if needed
    }

    return (
        <div className="flex flex-col sm:flex-row gap-4 items-start sm:items-center justify-between">
            {/* Right side - Search */}
            <div className="flex items-center gap-2 w-full">
                <TbSearch className="w-4 h-4 text-gray-500" />
                <Input
                    type="text"
                    placeholder={t('nav.shared.search')}
                    value={searchTerm}
                    className="min-w-[200px]"
                    onChange={handleSearchChange}
                />
            </div>

            {/* Left side - Filters */}
            <div className="flex flex-col sm:flex-row gap-3 items-start sm:items-center">
                {/* Organization Filter */}
                <div className="flex items-center gap-2">
                    <TbFilter className="w-4 h-4 text-gray-500" />
                    <Select
                        value={organizationOptions.find(
                            (opt) => opt.value === selectedOrganizationId,
                        )}
                        options={organizationOptions}
                        placeholder={t('nav.shared.selectOrganization')}
                        isLoading={isLoadingOrgs}
                        className="min-w-[300px]"
                        onChange={handleOrganizationChange}
                    />
                </div>

                {/* Status Filter */}
                <div className="flex items-center gap-2">
                    <BoxStatusSelect
                        value={selectedStatus}
                        includeAll={true}
                        className="min-w-[150px]"
                        allowedStatuses={[1, 2]}
                        onChange={handleStatusChange}
                    />
                </div>
            </div>
        </div>
    )
}

export default AllBoxesTableTools
